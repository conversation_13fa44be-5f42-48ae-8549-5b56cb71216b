# نظام الرصد الإعلامي
## Media Monitoring System

نظام شامل لإدارة ومتابعة الأخبار باللغة العربية، مطور باستخدام Flask و Bootstrap.

## الميزات الرئيسية

### 📰 إدارة الأخبار
- إضافة وتعديل وحذف الأخبار
- تصنيف الأخبار حسب الفئات والمحافظات
- حقول ديناميكية قابلة للتخصيص
- عرض الأخبار بتخطيط احترافي

### 📊 الإحصائيات والتقارير
- إحصائيات شاملة للأخبار والتصنيفات
- تقارير قابلة للطباعة
- تصدير إلى PDF
- فلترة حسب التاريخ والفئة

### 🏛️ إدارة التصنيفات والمحافظات
- إدارة تصنيفات الأخبار
- إدارة المحافظات العراقية
- حقول ديناميكية لكل تصنيف

### 🔧 إدارة النظام
- نسخ احتياطية لقاعدة البيانات
- استعادة البيانات
- إعدادات النظام

## متطلبات النظام

- Python 3.7 أو أحدث
- Flask 2.0+
- SQLAlchemy
- Bootstrap 5
- wkhtmltopdf (للتصدير إلى PDF)

## التنصيب

### التنصيب من الحزمة
```bash
pip install media-monitoring-system
```

### التنصيب من المصدر
```bash
git clone https://github.com/Yaser25m/news.git
cd news
pip install -e .
```

## التشغيل

### تشغيل التطبيق
```bash
# باستخدام الأمر المباشر
media-monitor

# أو باستخدام Python
python run.py
```

### الوصول للتطبيق
افتح المتصفح وانتقل إلى: `http://127.0.0.1:5000`

## الاستخدام

### إضافة خبر جديد
1. انقر على "إضافة خبر" من القائمة الرئيسية
2. املأ البيانات المطلوبة
3. اختر التصنيف والمحافظة
4. املأ الحقول الديناميكية حسب التصنيف
5. احفظ الخبر

### عرض الإحصائيات
1. انقر على "الإحصائيات" من القائمة الرئيسية
2. اختر الفترة الزمنية المطلوبة
3. اعرض أو اطبع التقرير

### إدارة التصنيفات
1. انتقل إلى "إدارة التصنيفات"
2. أضف تصنيفات جديدة أو عدل الموجودة
3. حدد الحقول الديناميكية لكل تصنيف

## التطوير

### هيكل المشروع
```
news/
├── app/
│   ├── static/          # ملفات CSS, JS, الصور
│   ├── templates/       # قوالب HTML
│   ├── models/          # نماذج قاعدة البيانات
│   ├── forms/           # نماذج Flask-WTF
│   └── utils/           # أدوات مساعدة
├── config.py            # إعدادات التطبيق
├── run.py              # نقطة دخول التطبيق
└── requirements.txt     # متطلبات Python
```

### المساهمة
نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## الترخيص
هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## الدعم
للحصول على الدعم أو الإبلاغ عن مشاكل، يرجى إنشاء Issue في GitHub.

## المطورون
- فريق نظام الرصد الإعلامي

---
© 2025 نظام الرصد الإعلامي. جميع الحقوق محفوظة.
