{% extends "layout.html" %}

{% block content %}
<div class="container py-4">
    <!-- شريط التنقل -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ url_for('index') }}">الرئيسية</a></li>
            <li class="breadcrumb-item"><a href="{{ url_for('view_news') }}">الأخبار</a></li>
            <li class="breadcrumb-item active" aria-current="page">طباعة الأخبار</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h3 class="mb-0"><i class="fas fa-print me-2"></i>طباعة الأخبار وتصديرها إلى PDF</h3>
                    <div class="mt-2 small">{{ today_date }}</div>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ url_for('print_news') }}" class="mb-4">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="start_date" class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="start_date" name="start_date" value="{{ request.args.get('start_date', '') }}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="end_date" class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="end_date" name="end_date" value="{{ request.args.get('end_date', '') }}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="category_id" class="form-label">التصنيف</label>
                                <select class="form-select" id="category_id" name="category_id">
                                    <option value="">جميع التصنيفات</option>
                                    {% for category in categories %}
                                        <option value="{{ category.id }}" {% if request.args.get('category_id')|int == category.id %}selected{% endif %}>{{ category.name }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search me-1"></i> بحث
                                </button>
                                {% if news_list %}
                                <a href="{{ url_for('export_news_pdf', start_date=request.args.get('start_date', ''), end_date=request.args.get('end_date', ''), category_id=request.args.get('category_id', '')) }}" class="btn btn-success ms-2">
                                    <i class="fas fa-file-pdf me-1"></i> تصدير إلى PDF
                                </a>
                                <button type="button" class="btn btn-info ms-2" onclick="window.print()">
                                    <i class="fas fa-print me-1"></i> طباعة
                                </button>
                                {% endif %}
                            </div>
                        </div>
                    </form>

                    {% if news_list %}
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>#</th>
                                        <th>العنوان</th>
                                        <th>التصنيف</th>
                                        <th>المحافظة</th>
                                        <th>التاريخ</th>
                                        <th>المصدر</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for news in news_list %}
                                        <tr>
                                            <td>{{ loop.index }}</td>
                                            <td>{{ news.title }}</td>
                                            <td>{{ news.category.name }}</td>
                                            <td>{{ news.governorate.name }}</td>
                                            <td>{{ news.date|format_date_arabic }}</td>
                                            <td>{{ news.source }}</td>
                                            <td>
                                                <a href="{{ url_for('view_news_details', news_id=news.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% elif request.args.get('start_date') or request.args.get('end_date') or request.args.get('category_id') %}
                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i> لا توجد أخبار تطابق معايير البحث.
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    @media print {
        .navbar, .news-ticker-container, .breadcrumb, .card-header, form, .no-print, footer {
            display: none !important;
        }
        .card {
            border: none !important;
            box-shadow: none !important;
        }
        .card-body {
            padding: 0 !important;
        }
        body {
            font-size: 12pt;
        }
        th, td {
            padding: 5px !important;
        }
    }
</style>
{% endblock %}
