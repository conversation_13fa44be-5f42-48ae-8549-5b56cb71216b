{% extends "layout.html" %}

{% block styles %}
<style>
    /* تصميم مشابه لموقع هذا اليوم */
    .news-header {
        background-color: #0056b3;
        color: white;
        padding: 10px 0;
        margin-bottom: 20px;
    }
    
    .news-title {
        color: #dc3545;
        font-weight: bold;
    }
    
    .news-source {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .news-date {
        color: #6c757d;
        font-size: 0.8rem;
    }
    
    .news-card {
        margin-bottom: 20px;
        border: 1px solid #dee2e6;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .news-card:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .news-card .card-body {
        padding: 15px;
    }
    
    .governorates-list {
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 5px;
        margin-bottom: 20px;
    }
    
    .governorates-list h3 {
        color: #0056b3;
        border-bottom: 2px solid #0056b3;
        padding-bottom: 10px;
        margin-bottom: 15px;
    }
    
    .governorates-list ul {
        list-style: none;
        padding: 0;
        display: flex;
        flex-wrap: wrap;
    }
    
    .governorates-list li {
        margin-left: 15px;
        margin-bottom: 10px;
    }
    
    .governorates-list a {
        color: #495057;
        text-decoration: none;
        transition: all 0.3s;
    }
    
    .governorates-list a:hover {
        color: #0056b3;
        text-decoration: underline;
    }
    
    .breaking-news {
        background-color: #dc3545;
        color: white;
        padding: 5px 10px;
        border-radius: 3px;
        font-size: 0.8rem;
        margin-left: 10px;
    }
    
    .categories-nav {
        background-color: #343a40;
        padding: 10px 0;
        margin-bottom: 20px;
    }
    
    .categories-nav ul {
        list-style: none;
        padding: 0;
        margin: 0;
        display: flex;
        flex-wrap: wrap;
    }
    
    .categories-nav li {
        margin-left: 20px;
    }
    
    .categories-nav a {
        color: white;
        text-decoration: none;
        transition: all 0.3s;
    }
    
    .categories-nav a:hover {
        color: #17a2b8;
    }
    
    .news-section-title {
        color: #0056b3;
        border-bottom: 2px solid #0056b3;
        padding-bottom: 10px;
        margin-bottom: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="categories-nav">
    <div class="container">
        <ul>
            <li><a href="{{ url_for('index') }}">الرئيسية</a></li>
            {% for category in categories %}
            <li><a href="{{ url_for('view_news_by_category', category_id=category.id) }}">{{ category.name }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="governorates-list">
    <div class="container">
        <h3>أخبار العراق الآن</h3>
        <ul>
            {% for governorate in governorates %}
            <li><a href="{{ url_for('view_news_by_governorate', governorate_id=governorate.id) }}">{{ governorate.name }}</a></li>
            {% endfor %}
        </ul>
    </div>
</div>

<div class="container">
    <h2 class="news-section-title">آخر الأخبار - تغطية مباشرة</h2>
    
    <div class="row">
        {% for news in latest_news %}
        <div class="col-md-12">
            <div class="news-card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span class="news-date">منذ {{ ((now - news.created_at).total_seconds() / 3600)|int }} ساعة</span>
                        <div>
                            <a href="#" class="me-2"><i class="fab fa-twitter"></i></a>
                            <a href="#"><i class="fab fa-facebook"></i></a>
                        </div>
                    </div>
                    <h5 class="news-title">
                        <a href="{{ url_for('view_news_details', news_id=news.id) }}" class="text-decoration-none">{{ news.title }}</a>
                    </h5>
                    <p class="news-source">
                        <a href="{{ news.source_url }}" target="_blank" class="text-decoration-none">{{ news.source }}</a>
                    </p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    
    <div class="text-center mt-4 mb-5">
        <a href="{{ url_for('view_news') }}" class="btn btn-primary">المزيد من الأخبار</a>
    </div>
    
    <div class="row">
        <div class="col-md-8">
            <h2 class="news-section-title">أخبار مميزة</h2>
            
            <div class="row">
                {% for news in featured_news %}
                <div class="col-md-6">
                    <div class="news-card">
                        <div class="card-body">
                            <h5 class="news-title">
                                <a href="{{ url_for('view_news_details', news_id=news.id) }}" class="text-decoration-none">{{ news.title }}</a>
                            </h5>
                            <p class="news-content">{{ news.content|truncate(100) }}</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="news-date">{{ news.date|format_date_arabic }}</span>
                                <span class="news-source">{{ news.source }}</span>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        
        <div class="col-md-4">
            <h2 class="news-section-title">تصنيفات الأخبار</h2>
            
            <div class="list-group mb-4">
                {% for category in categories %}
                <a href="{{ url_for('view_news_by_category', category_id=category.id) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    {{ category.name }}
                    <span class="badge bg-primary rounded-pill">{{ category.news.count() }}</span>
                </a>
                {% endfor %}
            </div>
            
            <h2 class="news-section-title">محافظات العراق</h2>
            
            <div class="list-group">
                {% for governorate in governorates %}
                <a href="{{ url_for('view_news_by_governorate', governorate_id=governorate.id) }}" class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                    {{ governorate.name }}
                    <span class="badge bg-primary rounded-pill">{{ governorate.news.count() }}</span>
                </a>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
