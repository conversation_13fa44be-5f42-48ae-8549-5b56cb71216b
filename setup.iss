[Setup]
AppName=Media Monitoring System
AppVersion=1.0.0
AppPublisher=Media Monitoring Team
AppPublisherURL=https://github.com/Yaser25m/news
DefaultDirName={autopf}\MediaMonitoringSystem
DefaultGroupName=Media Monitoring System
OutputDir=installer
OutputBaseFilename=MediaMonitoringSystem-Setup-1.0.0
Compression=lzma
SolidCompression=yes
WizardStyle=modern
PrivilegesRequired=admin

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked

[Files]
Source: "run.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "app.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "config.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "requirements.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "app\*"; DestDir: "{app}\app"; Flags: ignoreversion recursesubdirs createallsubdirs
Source: "app.db"; DestDir: "{app}"; Flags: ignoreversion
Source: "README.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "LICENSE"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\Media Monitoring System"; Filename: "python"; Parameters: """{app}\run.py"""; WorkingDir: "{app}"
Name: "{autodesktop}\Media Monitoring System"; Filename: "python"; Parameters: """{app}\run.py"""; WorkingDir: "{app}"; Tasks: desktopicon
Name: "{group}\{cm:UninstallProgram,Media Monitoring System}"; Filename: "{uninstallexe}"

[Run]
Filename: "pip"; Parameters: "install -r ""{app}\requirements.txt"""; StatusMsg: "Installing requirements..."; Flags: runhidden waituntilterminated
Filename: "python"; Parameters: """{app}\run.py"""; Description: "{cm:LaunchProgram,Media Monitoring System}"; Flags: nowait postinstall skipifsilent; WorkingDir: "{app}"

[Code]
function InitializeSetup(): Boolean;
var
  ResultCode: Integer;
begin
  Result := True;
  
  if not Exec('python', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
  begin
    MsgBox('Python is not installed. Please install Python 3.7+ from https://python.org', mbError, MB_OK);
    Result := False;
    Exit;
  end;
  
  if not Exec('pip', '--version', '', SW_HIDE, ewWaitUntilTerminated, ResultCode) then
  begin
    MsgBox('pip is not available. Please install pip first.', mbError, MB_OK);
    Result := False;
    Exit;
  end;
end;
