@echo off
echo Building Media Monitoring System Installer...
echo.

echo Checking Inno Setup...
where iscc >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Inno Setup not found
    echo Please install from: https://jrsoftware.org/isinfo.php
    pause
    exit /b 1
)

echo Creating installer directory...
if not exist "installer" mkdir "installer"

echo Building installer...
iscc setup.iss

if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: Installer created at installer\MediaMonitoringSystem-Setup-1.0.0.exe
) else (
    echo.
    echo ERROR: Build failed
)

pause
